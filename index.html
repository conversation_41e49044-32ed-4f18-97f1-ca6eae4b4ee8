<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中国黄金首饰价格一览表</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 40px 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .container {
            width: 100%;
            max-width: 1000px;
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            font-size: 32px;
            margin-bottom: 10px;
            position: relative;
            display: inline-block;
        }
        
        .header h1::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80%;
            height: 3px;
            background: linear-gradient(90deg, #d4af37, #f5c71a, #d4af37);
        }
        
        .subtitle {
            color: #666;
            font-size: 16px;
            margin-top: 15px;
        }
        
        .tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 10px 20px;
            margin: 0 5px;
            background-color: #f5f5f5;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .tab.active {
            background: linear-gradient(90deg, #d4af37, #f5c71a);
            color: #333;
            font-weight: bold;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .price-table {
            width: 100%;
            border-collapse: collapse;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .price-table thead {
            background: linear-gradient(90deg, #d4af37, #f5c71a);
            color: #333;
        }
        
        .price-table th {
            padding: 15px;
            text-align: center;
            font-weight: bold;
        }
        
        .price-table td {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }
        
        .price-table tbody tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .price-table tbody tr:hover {
            background-color: #f5f5f5;
        }
        
        .gold-price {
            font-weight: bold;
            color: #d4af37;
        }
        
        .platinum-price {
            font-weight: bold;
            color: #697689;
        }
        
        .bar-price {
            font-weight: bold;
            color: #cd7f32;
        }
        
        .footer {
            margin-top: 30px;
            text-align: center;
            color: #777;
            font-size: 14px;
        }
        
        .update-time {
            color: #555;
            font-weight: bold;
        }
        
        .chart-container {
            margin-top: 40px;
            width: 100%;
            height: 420px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
        }
        
        .chart-title {
            text-align: center;
            margin-bottom: 20px;
            color: #333;
            font-size: 18px;
        }
        
        .trend-up {
            color: #e15241;
        }
        
        .trend-down {
            color: #2cb57e;
        }
        
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            width: 100%;
        }
        
        .loading-spinner {
            border: 5px solid #f3f3f3;
            border-top: 5px solid #d4af37;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-message {
            text-align: center;
            color: #e15241;
            padding: 20px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .price-table th,
            .price-table td {
                padding: 10px 5px;
                font-size: 14px;
            }
            
            .tabs {
                flex-wrap: wrap;
            }
            
            .tab {
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>中国黄金首饰价格一览表--刀客源码网提供</h1>
            <p class="subtitle">实时获取全国黄金、铂金及金条最新报价</p>
        </div>
        
        <div class="tabs">
            <div class="tab active" data-tab="mainland">内地金店</div>
            <div class="tab" data-tab="hongkong">香港金店</div>
            <div class="tab" data-tab="international">国际黄金</div>
            <div class="tab" data-tab="domestic">国内黄金</div>
        </div>
        
        <div id="loading" class="loading">
            <div class="loading-spinner"></div>
        </div>
        
        <div id="error" class="error-message" style="display: none;">
            数据加载失败，请稍后再试
        </div>
        
        <div id="mainland" class="tab-content active" style="display: none;">
            <table class="price-table">
                <thead>
                    <tr>
                        <th>品牌</th>
                        <th>黄金价格</th>
                        <th>铂金价格</th>
                        <th>金条价格</th>
                        <th>单位</th>
                        <th>报价时间</th>
                    </tr>
                </thead>
                <tbody id="mainland-body">
                </tbody>
            </table>
        </div>
        
        <div id="hongkong" class="tab-content" style="display: none;">
            <table class="price-table">
                <thead>
                    <tr>
                        <th>品牌</th>
                        <th>黄金价格</th>
                        <th>铂金价格</th>
                        <th>金条价格</th>
                        <th>单位</th>
                        <th>报价时间</th>
                    </tr>
                </thead>
                <tbody id="hongkong-body">
                </tbody>
            </table>
        </div>
        
        <div id="international" class="tab-content" style="display: none;">
            <table class="price-table">
                <thead>
                    <tr>
                        <th>品种</th>
                        <th>最新价</th>
                        <th>涨跌</th>
                        <th>幅度</th>
                        <th>最高价</th>
                        <th>最低价</th>
                        <th>报价时间</th>
                    </tr>
                </thead>
                <tbody id="international-body">
                </tbody>
            </table>
        </div>
        
        <div id="domestic" class="tab-content" style="display: none;">
            <table class="price-table">
                <thead>
                    <tr>
                        <th>品种</th>
                        <th>最新价</th>
                        <th>涨跌</th>
                        <th>幅度</th>
                        <th>最高价</th>
                        <th>最低价</th>
                        <th>报价时间</th>
                    </tr>
                </thead>
                <tbody id="domestic-body">
                </tbody>
            </table>
        </div>
        
        <div class="footer">
            <p>最后更新时间: <span id="update-time" class="update-time">加载中...</span></p>
        </div>
    </div>
    
    <div class="container chart-container">
        <div id="chart">
            <img src="https://img.huilvbiao.com/chart_img/30.png?d=12" alt="黄金价格走势图" style="
    width: 100%;
">
        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            fetchGoldData();
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    tabs.forEach(t => t.classList.remove('active'));
                    document.querySelectorAll('.tab-content').forEach(content => {
                        content.classList.remove('active');
                        content.style.display = 'none';
                    });
                    this.classList.add('active');
                    const tabId = this.getAttribute('data-tab');
                    const tabContent = document.getElementById(tabId);
                    tabContent.classList.add('active');
                    tabContent.style.display = 'block';
                });
            });
        });
        function fetchGoldData() {
            document.getElementById('loading').style.display = 'flex';
            document.getElementById('mainland').style.display = 'none';
            document.getElementById('hongkong').style.display = 'none';
            document.getElementById('international').style.display = 'none';
            document.getElementById('domestic').style.display = 'none';
            document.getElementById('error').style.display = 'none';
            fetch('api.php')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络响应不正常');
                    }
                    return response.json();
                })
                .then(data => {
                    document.getElementById('loading').style.display = 'none';
                    if (data.code === 200) {
                        processGoldData(data);
                        document.getElementById('mainland').style.display = 'block';
                        const now = new Date();
                        document.getElementById('update-time').textContent = now.toLocaleString();
                    } else {
                        throw new Error('数据格式不正确');
                    }
                })
                .catch(error => {
                    console.error('获取数据失败:', error);
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('error').style.display = 'block';
                });
        }
        function processGoldData(data) {
            const mainlandData = data['国内十大金店'].filter(item => !item['品牌'].includes('香港'));
            const mainlandBody = document.getElementById('mainland-body');
            mainlandBody.innerHTML = '';
            mainlandData.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item['品牌']}</td>
                    <td class="gold-price">${item['黄金价格']}</td>
                    <td class="platinum-price">${item['铂金价格']}</td>
                    <td class="bar-price">${item['金条价格']}</td>
                    <td>${item['单位']}</td>
                    <td>${item['报价时间']}</td>
                `;
                mainlandBody.appendChild(row);
            });
            const hongkongData = data['国内十大金店'].filter(item => item['品牌'].includes('香港'));
            const hongkongBody = document.getElementById('hongkong-body');
            hongkongBody.innerHTML = '';
            hongkongData.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item['品牌']}</td>
                    <td class="gold-price">${item['黄金价格']}</td>
                    <td class="platinum-price">${item['铂金价格']}</td>
                    <td class="bar-price">${item['金条价格']}</td>
                    <td>${item['单位']}</td>
                    <td>${item['报价时间']}</td>
                `;
                hongkongBody.appendChild(row);
            });
            const internationalBody = document.getElementById('international-body');
            internationalBody.innerHTML = '';
            data['国际黄金'].forEach(item => {
                const trendClass = parseFloat(item['涨跌']) >= 0 ? 'trend-up' : 'trend-down';
                const trend = parseFloat(item['涨跌']) >= 0 ? '+' + item['涨跌'] : item['涨跌']; 
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item['品种']}</td>
                    <td class="gold-price">${item['最新价']}</td>
                    <td class="${trendClass}">${trend}</td>
                    <td class="${trendClass}">${item['幅度']}</td>
                    <td>${item['最高价']}</td>
                    <td>${item['最低价']}</td>
                    <td>${item['报价时间']}</td>
                `;
                internationalBody.appendChild(row);
            });
            const domesticBody = document.getElementById('domestic-body');
            domesticBody.innerHTML = '';
            data['国内黄金'].forEach(item => {
                const trendClass = item['涨跌'] !== '-' && parseFloat(item['涨跌']) >= 0 ? 'trend-up' : 'trend-down';
                const trend = item['涨跌'] !== '-' ? (parseFloat(item['涨跌']) >= 0 ? '+' + item['涨跌'] : item['涨跌']) : '-';
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item['品种']}</td>
                    <td class="gold-price">${item['最新价']}</td>
                    <td class="${trendClass}">${trend}</td>
                    <td class="${trendClass}">${item['幅度']}</td>
                    <td>${item['最高价']}</td>
                    <td>${item['最低价']}</td>
                    <td>${item['报价时间']}</td>
                `;
                domesticBody.appendChild(row);
            });
        }
    </script>

</body>
</html>